/* ===== TEWUNEED MODERN DESIGN SYSTEM ===== */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap');

/* CSS Variables - Modern Color Palette */
:root {
    /* Primary Brand Colors */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* Secondary Colors */
    --secondary-50: #f8fafc;
    --secondary-100: #f1f5f9;
    --secondary-200: #e2e8f0;
    --secondary-300: #cbd5e1;
    --secondary-400: #94a3b8;
    --secondary-500: #64748b;
    --secondary-600: #475569;
    --secondary-700: #334155;
    --secondary-800: #1e293b;
    --secondary-900: #0f172a;

    /* Accent Colors */
    --accent-orange: #f97316;
    --accent-green: #10b981;
    --accent-purple: #8b5cf6;
    --accent-pink: #ec4899;

    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;

    /* Neutral Colors */
    --white: #ffffff;
    --black: #000000;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Typography */
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-serif: 'Playfair Display', Georgia, serif;
    --font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;

    /* Font Sizes */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;

    /* Border Radius */
    --radius-sm: 0.125rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;

    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

/* ===== GLOBAL RESET & BASE STYLES ===== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family-sans);
    font-size: var(--text-base);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.main-content {
    min-height: calc(100vh - 200px);
}

/* ===== TYPOGRAPHY ===== */

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-serif);
    font-weight: 600;
    line-height: 1.2;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

p {
    margin-bottom: var(--space-4);
    color: var(--gray-700);
}

a {
    color: var(--primary-600);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-700);
}

/* Navigation Styles */
.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
    padding: 0.5rem 1rem;
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
    transform: translateY(-1px);
}

.dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: var(--border-radius);
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Search Bar */
.navbar .input-group {
    width: 250px;
}

.navbar .form-control {
    border: 1px solid rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.navbar .form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.navbar .form-control:focus {
    background-color: white;
    color: var(--dark-color);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Cart Badge */
.navbar .badge {
    font-size: 0.7rem;
    min-width: 1.2rem;
    height: 1.2rem;
    line-height: 1.2rem;
    padding: 0;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 4rem 0;
    margin-bottom: 3rem;
}

.hero-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-img-top {
    height: 200px;
    object-fit: cover;
    transition: var(--transition);
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.card-text {
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

/* Product Cards */
.product-card {
    position: relative;
    height: 100%;
}

.product-card .badge {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
}

.product-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.product-price-old {
    text-decoration: line-through;
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-left: 0.5rem;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.btn-sm {
    padding: 0.25rem 1rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
}

/* Forms */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.5rem;
}

.alert-dismissible .btn-close {
    padding: 1.25rem 1.5rem;
}

/* Footer */
footer {
    margin-top: auto;
}

footer h5, footer h6 {
    color: white;
}

footer .social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

footer .social-links a:hover {
    background-color: var(--primary-color);
    transform: translateY(-2px);
}

footer .payment-methods i {
    font-size: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .hero-content p {
        font-size: 1rem;
    }
    
    .navbar .input-group {
        width: 200px;
    }
    
    .card-img-top {
        height: 150px;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 2rem 0;
    }
    
    .hero-content h1 {
        font-size: 1.5rem;
    }
    
    .navbar .input-group {
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .navbar-nav {
        text-align: center;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

.shadow-sm {
    box-shadow: var(--box-shadow) !important;
}

.rounded {
    border-radius: var(--border-radius) !important;
}

/* ===== MODERN COMPONENT STYLES ===== */

/* ===== HEADER STYLES ===== */

/* Ensure header is always visible */
header {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%) !important;
    box-shadow: var(--shadow-lg) !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 1030 !important;
}

/* Navbar Brand */
.navbar-brand {
    font-family: var(--font-family-serif) !important;
    font-size: var(--text-2xl) !important;
    font-weight: 700 !important;
    color: var(--white) !important;
    text-decoration: none !important;
}

/* Navigation Links */
.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500 !important;
    padding: var(--space-3) var(--space-4) !important;
    border-radius: var(--radius-lg) !important;
    transition: var(--transition-fast) !important;
    position: relative !important;
}

.navbar-nav .nav-link:hover {
    color: var(--white) !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
    transform: translateY(-1px) !important;
}

/* Dropdown Menus */
.dropdown-menu {
    border: none !important;
    box-shadow: var(--shadow-xl) !important;
    border-radius: var(--radius-lg) !important;
    padding: var(--space-2) 0 !important;
}

.dropdown-item {
    padding: var(--space-3) var(--space-4) !important;
    transition: var(--transition-fast) !important;
}

.dropdown-item:hover {
    background-color: var(--primary-50) !important;
    color: var(--primary-700) !important;
}

/* Cart Badge */
.badge {
    font-size: 0.7rem !important;
    padding: 0.25rem 0.5rem !important;
}

/* Mobile Toggle */
.navbar-toggler {
    border: none !important;
    padding: 0.25rem 0.5rem !important;
}

.navbar-toggler:focus {
    box-shadow: none !important;
}

/* Modern Search Bar */
.modern-search {
    position: relative;
    max-width: 400px;
}

.modern-search .form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--white);
    padding-left: var(--space-12);
    border-radius: var(--radius-full);
}

.modern-search .form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.modern-search .search-icon {
    position: absolute;
    left: var(--space-4);
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
    z-index: 10;
}

/* Modern Button Styles */
.btn-modern {
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: var(--text-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition-fast);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-normal);
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-modern-primary {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-modern-primary:hover {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--white);
}

.btn-modern-secondary {
    background: var(--white);
    color: var(--primary-600);
    border: 2px solid var(--primary-600);
    box-shadow: var(--shadow-sm);
}

.btn-modern-secondary:hover {
    background: var(--primary-600);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Modern Card Styles */
.card-modern {
    background: var(--white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: var(--transition-normal);
    overflow: hidden;
}

.card-modern:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
}

.card-modern .card-header {
    background: linear-gradient(135deg, var(--gray-50), var(--white));
    border-bottom: 1px solid var(--gray-200);
    padding: var(--space-6);
}

.card-modern .card-body {
    padding: var(--space-6);
}

.card-modern .card-footer {
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    padding: var(--space-4) var(--space-6);
}

/* Modern Product Card */
.product-card-modern {
    background: var(--white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    position: relative;
    border: 1px solid var(--gray-200);
}

.product-card-modern:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.product-card-modern .product-image {
    position: relative;
    overflow: hidden;
    aspect-ratio: 1;
}

.product-card-modern .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.product-card-modern:hover .product-image img {
    transform: scale(1.1);
}

.product-card-modern .product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
    opacity: 0;
    transition: var(--transition-normal);
    display: flex;
    align-items: flex-end;
    padding: var(--space-4);
}

.product-card-modern:hover .product-overlay {
    opacity: 1;
}

.product-card-modern .product-actions {
    display: flex;
    gap: var(--space-2);
    width: 100%;
}

.product-card-modern .product-info {
    padding: var(--space-5);
}

.product-card-modern .product-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
    line-height: 1.3;
}

.product-card-modern .product-price {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--primary-600);
    margin-bottom: var(--space-3);
}

.product-card-modern .product-rating {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-3);
}

.product-card-modern .stars {
    color: var(--warning);
}

.product-card-modern .rating-text {
    font-size: var(--text-sm);
    color: var(--gray-600);
}

/* ===== HERO SECTION ANIMATIONS ===== */

.hero-section {
    min-height: 80vh;
}

.floating-product {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

.hero-product-showcase {
    position: relative;
    height: 400px;
}

/* ===== RESPONSIVE UTILITIES ===== */

.min-vh-75 {
    min-height: 75vh;
}

.text-white-75 {
    color: rgba(255, 255, 255, 0.75) !important;
}

/* ===== MODERN FORM STYLES ===== */

.form-control-modern {
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    font-size: var(--text-base);
    transition: var(--transition-fast);
    background-color: var(--white);
}

.form-control-modern:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

.form-control-modern::placeholder {
    color: var(--gray-400);
}

/* ===== BADGE STYLES ===== */

.badge-modern {
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-full);
    font-weight: 600;
    font-size: var(--text-xs);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background-color: var(--primary-100);
    color: var(--primary-700);
}

.badge-success {
    background-color: var(--accent-green);
    color: var(--white);
}

.badge-warning {
    background-color: var(--warning);
    color: var(--white);
}

.badge-danger {
    background-color: var(--error);
    color: var(--white);
}

/* ===== LOADING STATES ===== */

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-200);
    border-radius: 50%;
    border-top-color: var(--primary-600);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ===== HOVER EFFECTS ===== */

.hover-lift {
    transition: var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.hover-scale {
    transition: var(--transition-normal);
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
    .hero-section {
        min-height: 60vh;
    }

    .hero-content h1 {
        font-size: var(--text-3xl);
    }

    .hero-product-showcase {
        height: 250px;
    }

    .floating-product {
        width: 80px !important;
    }

    .floating-product .card {
        width: 80px !important;
    }

    .floating-product img {
        height: 50px !important;
    }
}

@media (max-width: 576px) {
    .hero-section {
        min-height: 50vh;
    }

    .hero-content h1 {
        font-size: var(--text-2xl);
    }

    .hero-content p {
        font-size: var(--text-base);
    }

    .hero-product-showcase {
        display: none;
    }
}

/* ===== FOOTER STYLES ===== */

.hover-link {
    transition: var(--transition-fast);
}

.hover-link:hover {
    color: var(--warning) !important;
    transform: translateX(5px);
}



.social-links .btn:hover {
    background-color: var(--warning);
    border-color: var(--warning);
    color: var(--dark);
    transform: translateY(-2px);
}

.payment-methods i:hover {
    transform: scale(1.2);
    transition: var(--transition-fast);
}

/* ===== SECTION SPACING ===== */

section {
    position: relative;
}

.section-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--gray-300), transparent);
    margin: var(--space-8) 0;
}
